from playwright.sync_api import sync_playwright
from playwright.sync_api import Page, expect
import re
import time

playwright = None
browser = None
page = None

答案 = {}
课程 = [
    "语文(上)",
    "语文(下)",
    "数学(下)",
    "沟通技巧",
    "就业指导",
    "职业健康与安全",
    "数学(上)",
    "多媒体技术基础",
    "Office办公软件",
    "办公设备使用与维护",
    "职业应用写作",
    "历史",
    "个人与团队",
    "Windows操作系统",
    "学习指南",
    "计算机职业素养",
    "计算机常用工具软件",
    "计算机网络基础",
    "中国特色社会主义",
    "职业道德与法治",
    "哲学与人生",
    "心理健康与职业生涯",
    "信息技术"
]



def init_playwright(port=6666):
    """初始化 Playwright 和浏览器连接"""
    global playwright, browser, page

    try:
        # 启动 Playwright
        playwright = sync_playwright().start()

        # 尝试连接现有浏览器
        browser = playwright.chromium.connect_over_cdp(f"http://localhost:{port}")

        # 获取页面
        context = browser.contexts[0] if browser.contexts else browser.new_context()
        page = context.pages[0] if context.pages else context.new_page()
        page.bring_to_front()

        #page.pause()  # 进入录制模式

        print(f"已连接到页面: {page.url}")
        return True
    except Exception as e:
        print(f"连接失败: {e}")
        return False


def 导航到课程详情():
        global page, browser, playwright
        # 示例：在附加后执行操作
        page.goto("https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/home")
        page.get_by_text("查看全部").click()
        try:
            for i in range(1, 50):
                e = page.locator(f"div:nth-child({i}) > .module_course_info_container > .progressView > .linear_container > .linear_content > .bubble")
                t = e.inner_text()
                if t != "100%":
                    for j in 课程:
                        if page.locator(f"div:nth-child({i}) > .module_course_info_container > .majorName > div > span").inner_text() == j:
                            print(f"进入课程: {j}")
                            with page.expect_navigation():
                                page.locator(f"div:nth-child({i}) > .module_course_info_container > .majorName > div > span").click()
                            return True

        except Exception as e:
            print(f"默认50，不是致命错误: {e}")
            重置链接()

def 检测链接():
    global page, browser, playwright
    重置链接()
    print(f"当前链接: {page.url}")
    return page.url

def 重置链接():
    global playwright, browser, page, 答案
    browser.close()
    playwright.stop()
    playwright = None
    browser = None
    page = None
    init_playwright()
    答案 = {}


def 总检测逻辑():
    global page, browser, playwright
    链接 = 检测链接()
    if "https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/courseStudy" in 链接:
        print("成功进入课程详情")
        print("尝试进入视频或答题页面")
        导航到视频或答题页面()
        总检测逻辑()
    elif "https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/videoStudy" in 链接:
        print("成功进入视频页面")
        视频处理()
        导航到课程详情()
        总检测逻辑()
    elif "https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/exam/answer" in 链接:
        print("成功进入答题页面")
        获取答案前的步骤()
        总检测逻辑()
    elif "https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/exam/testPaper" in 链接:
        print("成功进入答案页面")
        获取答案()
        导航到课程详情()
        导航到视频或答题页面()
        print("开始填写答案")
        答题()
        总检测逻辑()
    else:
        print("尝试进入课程详情")
        导航到课程详情()
        总检测逻辑()



def 导航到视频或答题页面():
    global page, browser, playwright
    try:
        for i in range(1, 50):
            if page.locator(f"#van-tab-2 > div > div > div > div:nth-child({i}) > div > div > span.title_vice").inner_text() == "未完成":
                page.locator(f"#van-tab-2 > div > div > div > div:nth-child({i}) > div > div > span.title_vice").click()
                for j in range(1, 50):
                    for k in range(1, 50):
                        page.locator(f"#van-tab-2 > div > div > div > div:nth-child({j}) > div.van-collapse-item__wrapper > div > div:nth-child({k}) > div > div > div").click()
                        for l in range(1, 50):
                            for m in range(1, 50):
                                if page.locator(f"#van-tab-2 > div > div > div > div:nth-child({l}) > div.van-collapse-item__wrapper > div > div:nth-child({m}) > div > div.van-collapse-item__wrapper > div > div > div > div.flex.itemTitle > span").count() > 0:
                                    if page.locator(f"#van-tab-2 > div > div > div > div:nth-child({l}) > div.van-collapse-item__wrapper > div > div:nth-child({m}) > div > div.van-collapse-item__wrapper > div > div > div > div.flex.itemTitle > span").inner_text() != "已完成":
                                        print("1")
                                        page.locator(f"#van-tab-2 > div > div > div > div:nth-child({l}) > div.van-collapse-item__wrapper > div > div:nth-child({m}) > div > div.van-collapse-item__wrapper > div > div > div > div.flex.itemTitle > span").click()
                                        return True
                                elif page.locator(f"#van-tab-2 > div > div > div > div:nth-child({l}) > div.van-collapse-item__wrapper > div > div:nth-child({m}) > div > div.van-collapse-item__wrapper > div > div > div > div > span").count() > 0:
                                    if page.locator(f"#van-tab-2 > div > div > div > div:nth-child({l}) > div.van-collapse-item__wrapper > div > div:nth-child({m}) > div > div.van-collapse-item__wrapper > div > div > div > div > span").inner_text() != "已完成":
                                        print("2")
                                        page.locator(f"#van-tab-2 > div > div > div > div:nth-child({l}) > div.van-collapse-item__wrapper > div > div:nth-child({m}) > div > div.van-collapse-item__wrapper > div > div > div > div > span").click()
                                        return True
                                else:
                                    print("0")
                                    break


    except Exception as e:
        print(f"可能当前课程已完成: {e}")
        print("重新导航")
        导航到课程详情()

def 视频处理():
    global page, browser, playwright
    while True:
        time.sleep(3)
        a = page.query_selector('//*[@id="xgPlayer"]/xg-controls/xg-inner-controls/xg-left-grid/xg-icon[2]/span[1]/span[1]').text_content()
        b = page.query_selector('//*[@id="xgPlayer"]/xg-controls/xg-inner-controls/xg-left-grid/xg-icon[2]/span[1]/span[2]').text_content()
        ab = a+":"+b
        c = page.query_selector("#xgPlayer > xg-controls > xg-inner-controls > xg-left-grid > xg-icon.xgplayer-time > span.time-duration").text_content()
        if c == ab:
            print("视频播放完成")
            time.sleep(3)
            return True
        print(f"当前播放时间: {ab} -> {c}")

def 获取答案前的步骤():
    global page, browser, playwright
    print("开始处理获取答案前的步骤")
    p = page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.subject > div.curAnser").inner_text()
    c = re.search(r'/(\d+)', p)
    print("总题目数："+c.group(1))
    for i in range(1, int(c.group(1)) + 1):
        page.locator(f"#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.amswerOption > div:nth-child(1) > div.optionIndex").click()
        page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.questionBtn > div:nth-child(2)").click()
    page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.pageEnd > div").click()
    page.locator("#app > div > div.layout-content.noShowTab > div.van-overlay > div > div > div.dialog-footer > button.van-button.van-button--default.van-button--large.determine").click()
    time.sleep(3)
    page.locator("#app > div > div.layout-content.noShowTab > div.van-overlay > div > div > div.dialog-footer > button > div > span").click()

def 获取答案():
    global page, browser, playwright, 答案
    print("开始获取答案")
    p = page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.subject > div.curAnser").inner_text()
    c = re.search(r'/(\d+)', p)
    print("总题目数：" + c.group(1))
    for i in range(1, int(c.group(1)) + 1):
        f = page.locator('#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.answerTitle').inner_text()
        # 清理题目文本，去除多余空格和换行符
        f = f.strip()
        x = page.locator('.everyOption.answerSort').inner_text()
        pattern = r'\n(.*)'  # 匹配第一个 \n 后的所有内容
        g = re.search(pattern, x, re.DOTALL)  # re.DOTALL 使 . 包括换行符
        答案[f] = g.group(1).strip()  # 也清理答案文本
        print(f"📝 题目{i}: '{f}' -> 答案: '{答案[f]}'")
        page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.questionBtn > div:nth-child(2)").click()
    print("🎯 完整答案字典:")
    for 题目, 答案内容 in 答案.items():
        print(f"  '{题目}' -> '{答案内容}'")

def 答题():
    global page, browser, playwright, 答案
    链接 = 检测链接()
    if "https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/exam/answer" in 链接:
        print("开始答题")
    else:
        总检测逻辑()
    p = page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.subject > div.curAnser").inner_text()
    c = re.search(r'/(\d+)', p)
    print("总题目数：" + c.group(1))

    for i in range(1, int(c.group(1)) + 1):
        f = page.locator('#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.answerTitle').inner_text()
        # 清理题目文本，确保与答案字典中的键完全匹配
        f = f.strip()
        try:
            print(f"🔍 第{i}题题目：'{f}'")
            print(f"📖 字典中的答案：'{答案.get(f, '未找到')}'")

            # 检查题目是否在答案字典中
            if f not in 答案:
                print(f"❌ 题目不在答案字典中！")
                print("🔍 字典中的所有题目：")
                for 索引, 字典题目 in enumerate(答案.keys(), 1):
                    print(f"  {索引}. '{字典题目}'")
                print("⚠️  可能的原因：题目文本格式不匹配")

            下一题 = "#app > div > div.layout-content.noShowTab > div.answer > div.questionBtn > div:nth-child(2)"

            # 定义所有选项的选择器
            选项列表 = [
                "#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.amswerOption > div:nth-child(1) > div.selectContent",
                "#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.amswerOption > div:nth-child(2) > div.selectContent",
                "#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.amswerOption > div:nth-child(3) > div.selectContent",
                "#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.amswerOption > div:nth-child(4) > div.selectContent"
            ]

            找到答案 = False

            # 获取当前题目的正确答案
            当前答案 = None
            if f in 答案:
                当前答案 = 答案[f].strip()
            else:
                # 尝试模糊匹配
                print("🔍 尝试模糊匹配...")
                for 字典题目, 字典答案 in 答案.items():
                    if f in 字典题目 or 字典题目 in f:
                        当前答案 = 字典答案.strip()
                        print(f"✅ 模糊匹配成功：'{字典题目}' -> '{当前答案}'")
                        break

            if 当前答案 is None:
                print(f"❌ 无法找到题目答案，跳过此题")
                page.locator(下一题).click()
                continue

            # 遍历所有选项寻找匹配的答案
            for 选项索引, 选项选择器 in enumerate(选项列表, 1):
                if page.locator(选项选择器).count() > 0:
                    选项内容 = page.locator(选项选择器).inner_text().strip()
                    print(f"选项{选项索引}: '{选项内容}'")

                    if 选项内容 == 当前答案:
                        print(f"✅ 找到匹配答案！点击选项{选项索引}")
                        page.locator(选项选择器).click()
                        page.locator(下一题).click()
                        找到答案 = True
                        break  # 找到答案后跳出选项循环，进入下一题

            # 如果没有找到匹配的答案
            if not 找到答案:
                print(f"❌ 第{i}题没有找到匹配的答案，跳过此题")
                # 可以选择点击第一个选项或者跳过
                if page.locator(选项列表[0]).count() > 0:
                    print("默认选择第一个选项")
                    page.locator(选项列表[0]).click()
                page.locator(下一题).click()

        except Exception as e:
            print(f"第{i}题题目或答案匹配失败: {e}")
            print("重试")
            总检测逻辑()
    print("交卷")
    page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.pageEnd > div").click()
    page.locator("#app > div > div.layout-content.noShowTab > div.van-overlay > div > div > div.dialog-footer > button.van-button.van-button--default.van-button--large.determine").click()
    time.sleep(3)
    zz = page.locator("#app > div > div.layout-content.noShowTab > div.van-overlay > div > div > div.dialogContent > div.errorReson").inner_text()
    print(f"交卷结果: {zz}")
    page.locator("#app > div > div.layout-content.noShowTab > div.van-overlay > div > div > div.dialog-footer > button > div > span").click()
    导航到课程详情()

if __name__ == "__main__":
    init_playwright()
    总检测逻辑()

    print(page.url)

